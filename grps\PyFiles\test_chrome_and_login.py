#!/usr/bin/env python3
"""
Test script to verify Chrome security flag fix and login functionality
"""

import os
import sys
import time
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_chrome_flags():
    """Test that Chrome starts without the --disable-web-security warning"""
    logger.info("Testing Chrome flags...")
    
    try:
        # Import the enhanced driver
        from updated_groups import EnhancedSeleniumBaseDriver
        
        # Create a test instance
        test_email = "<EMAIL>"
        test_password = "test_password"
        test_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        
        logger.info("Creating test driver instance...")
        driver = EnhancedSeleniumBaseDriver(test_email, test_password, test_ua, 1)
        
        logger.info("Driver created successfully")
        
        # Navigate to a test page
        logger.info("Navigating to test page...")
        driver.browser.go("https://www.google.com")
        
        # Check if we can access the page without warnings
        current_url = driver.browser.this_url()
        logger.info(f"Successfully navigated to: {current_url}")
        
        # Wait a moment to see if any warnings appear
        time.sleep(3)
        
        # Check browser console for errors
        try:
            logs = driver.browser.get_log('browser')
            security_warnings = [log for log in logs if 'disable-web-security' in log.get('message', '').lower()]
            
            if security_warnings:
                logger.error("Found security warnings in browser console:")
                for warning in security_warnings:
                    logger.error(f"  {warning}")
                return False
            else:
                logger.info("✅ No security warnings found in browser console")
                
        except Exception as e:
            logger.warning(f"Could not check browser console: {e}")
        
        # Clean up
        driver.finish()
        logger.info("✅ Chrome flags test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chrome flags test failed: {e}")
        return False

def test_login_functionality():
    """Test the login functionality with a real account"""
    logger.info("Testing login functionality...")
    
    try:
        # Import required modules
        from groups import Worker
        
        # Test with a real account (you'll need to provide credentials)
        logger.info("Note: This test requires real Gmail credentials")
        logger.info("For security, this test will only check if the login process starts correctly")
        
        # Create a worker instance
        actions = ["login"]
        worker = Worker(actions)
        
        logger.info("✅ Login functionality test setup completed")
        logger.info("To fully test login, run the main script with real credentials")
        return True
        
    except Exception as e:
        logger.error(f"❌ Login functionality test failed: {e}")
        return False

def test_seleniumbase_configuration():
    """Test SeleniumBase configuration to ensure no problematic flags"""
    logger.info("Testing SeleniumBase configuration...")
    
    try:
        # Check if SeleniumBase is available
        try:
            from seleniumbase import SBDriver
            logger.info("SeleniumBase is available")
        except ImportError:
            logger.warning("SeleniumBase not available - using fallback driver")
            return True
        
        # Create a minimal SeleniumBase driver to check its configuration
        logger.info("Creating minimal SeleniumBase driver...")
        
        # Use minimal configuration
        driver = SBDriver(
            browser='chrome',
            headless=False,
            uc=True,
            disable_web_security=False  # Explicitly disable
        )
        
        logger.info("SeleniumBase driver created successfully")
        
        # Navigate to a test page
        driver.go("https://www.google.com")
        logger.info("Successfully navigated with SeleniumBase")
        
        # Wait a moment
        time.sleep(2)
        
        # Clean up
        driver.quit()
        logger.info("✅ SeleniumBase configuration test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ SeleniumBase configuration test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🚀 Starting Chrome and Login Tests...")
    logger.info("=" * 50)
    
    results = {}
    
    # Test 1: Chrome flags
    logger.info("Test 1: Chrome Security Flags")
    results['chrome_flags'] = test_chrome_flags()
    
    # Test 2: SeleniumBase configuration
    logger.info("\nTest 2: SeleniumBase Configuration")
    results['seleniumbase_config'] = test_seleniumbase_configuration()
    
    # Test 3: Login functionality
    logger.info("\nTest 3: Login Functionality")
    results['login_functionality'] = test_login_functionality()
    
    # Summary
    logger.info("=" * 50)
    logger.info("📊 Test Results Summary:")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 All tests passed!")
        logger.info("Chrome security warning should be resolved.")
        logger.info("Login functionality is ready for testing.")
    else:
        logger.error("\n⚠️ Some tests failed.")
        logger.error("Please check the errors above and fix them.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
