#!/usr/bin/env python3
"""
Comprehensive fix for Chrome security warning and login issues
"""

import os
import sys
import time
import json
import psutil
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def kill_all_chrome_processes():
    """Kill all Chrome processes to ensure clean start"""
    logger.info("Killing all Chrome processes...")
    
    killed_count = 0
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                proc_name = proc.info['name']
                if proc_name and ('chrome' in proc_name.lower() or 'chromedriver' in proc_name.lower()):
                    logger.info(f"Terminating: {proc_name} (PID: {proc.info['pid']})")
                    proc.terminate()
                    killed_count += 1
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        # Wait for processes to terminate
        time.sleep(3)
        
        # Force kill any remaining
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name']
                if proc_name and ('chrome' in proc_name.lower() or 'chromedriver' in proc_name.lower()):
                    logger.warning(f"Force killing: {proc_name}")
                    proc.kill()
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
    except Exception as e:
        logger.error(f"Error killing Chrome processes: {e}")
        
    logger.info(f"Terminated {killed_count} Chrome processes")

def clear_all_chrome_data():
    """Clear Chrome profile data and caches"""
    logger.info("Clearing Chrome profile data...")
    
    current_dir = Path(__file__).parent
    profiles_dir = current_dir.parent / "Profiles"
    
    if not profiles_dir.exists():
        logger.warning(f"Profiles directory not found: {profiles_dir}")
        return
        
    cleared_count = 0
    
    for profile_dir in profiles_dir.iterdir():
        if profile_dir.is_dir():
            try:
                # Clear various Chrome cache files
                cache_files = [
                    "Default/Preferences",
                    "Default/Secure Preferences", 
                    "Default/Local State",
                    "Local State",
                    "Default/Web Data",
                    "Default/Cookies",
                    "Default/Current Session",
                    "Default/Current Tabs"
                ]
                
                for cache_file in cache_files:
                    cache_path = profile_dir / cache_file
                    if cache_path.exists():
                        try:
                            if cache_file.endswith(("Preferences", "Local State")):
                                # Clean JSON files
                                with open(cache_path, 'r', encoding='utf-8') as f:
                                    data = json.load(f)
                                
                                # Remove problematic entries
                                if 'browser' in data:
                                    data['browser'].pop('enabled_labs_experiments', None)
                                    data['browser'].pop('command_line_flags', None)
                                    data['browser'].pop('command_line', None)
                                
                                with open(cache_path, 'w', encoding='utf-8') as f:
                                    json.dump(data, f, indent=2)
                                    
                            else:
                                # Remove other cache files
                                cache_path.unlink()
                                
                        except Exception as e:
                            logger.debug(f"Could not clean {cache_file}: {e}")
                            
                cleared_count += 1
                logger.info(f"Cleaned profile: {profile_dir.name}")
                
            except Exception as e:
                logger.warning(f"Error cleaning profile {profile_dir.name}: {e}")
                
    logger.info(f"Cleaned {cleared_count} profiles")

def verify_fixes():
    """Verify that fixes are in place"""
    logger.info("Verifying fixes...")
    
    # Check updated_groups.py
    current_dir = Path(__file__).parent
    updated_groups_file = current_dir / "updated_groups.py"
    
    if not updated_groups_file.exists():
        logger.error("updated_groups.py not found!")
        return False
        
    try:
        with open(updated_groups_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for fixes
        fixes_found = {
            'enable_web_security': '--enable-web-security' in content,
            'chrome_flags_verification': '_verify_chrome_flags' in content,
            'cleanup_methods': '_cleanup_chrome_processes' in content and '_clear_chrome_profile_flags' in content
        }
        
        logger.info("Fix verification results:")
        for fix_name, found in fixes_found.items():
            status = "✅" if found else "❌"
            logger.info(f"  {fix_name}: {status}")
            
        return all(fixes_found.values())
        
    except Exception as e:
        logger.error(f"Error verifying fixes: {e}")
        return False

def check_groups_py_fix():
    """Check if groups.py has the suspicious_activity fix"""
    logger.info("Checking groups.py for suspicious_activity fix...")
    
    current_dir = Path(__file__).parent
    groups_file = current_dir / "groups.py"
    
    if not groups_file.exists():
        logger.error("groups.py not found!")
        return False
        
    try:
        with open(groups_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for the fix
        if 'status == "suspicious_activity"' in content:
            logger.info("✅ suspicious_activity fix found in groups.py")
            return True
        else:
            logger.error("❌ suspicious_activity fix NOT found in groups.py")
            return False
            
    except Exception as e:
        logger.error(f"Error checking groups.py: {e}")
        return False

def test_quick_chrome_start():
    """Quick test to see if Chrome starts without warnings"""
    logger.info("Testing Chrome startup...")
    
    try:
        # Try to import and create a minimal driver
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        options = Options()
        options.add_argument('--headless')  # Run headless for quick test
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--enable-web-security')  # Explicitly enable
        options.add_argument('--disable-features=VizDisplayCompositor')
        
        service = Service(ChromeDriverManager().install())
        
        logger.info("Creating test Chrome driver...")
        driver = webdriver.Chrome(service=service, options=options)
        
        logger.info("Navigating to test page...")
        driver.get("https://www.google.com")
        
        title = driver.title
        logger.info(f"Page title: {title}")
        
        driver.quit()
        logger.info("✅ Chrome test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chrome test failed: {e}")
        return False

def main():
    """Main fix function"""
    logger.info("🚀 Starting Comprehensive Fix...")
    logger.info("=" * 60)
    
    # Step 1: Kill all Chrome processes
    kill_all_chrome_processes()
    
    # Step 2: Clear Chrome data
    clear_all_chrome_data()
    
    # Step 3: Verify code fixes
    code_fixes_ok = verify_fixes()
    groups_fix_ok = check_groups_py_fix()
    
    # Step 4: Test Chrome startup
    chrome_test_ok = test_quick_chrome_start()
    
    # Summary
    logger.info("=" * 60)
    logger.info("📊 Comprehensive Fix Results:")
    logger.info(f"  Code fixes: {'✅' if code_fixes_ok else '❌'}")
    logger.info(f"  Groups.py fix: {'✅' if groups_fix_ok else '❌'}")
    logger.info(f"  Chrome test: {'✅' if chrome_test_ok else '❌'}")
    
    all_ok = code_fixes_ok and groups_fix_ok and chrome_test_ok
    
    if all_ok:
        logger.info("\n🎉 All fixes applied successfully!")
        logger.info("✅ Chrome security warning should be resolved")
        logger.info("✅ Login functionality should work for suspicious_activity accounts")
        logger.info("✅ Chrome starts without problematic flags")
        logger.info("\n🚀 Ready to test with your application!")
    else:
        logger.error("\n⚠️ Some fixes failed or are incomplete")
        logger.error("Please check the errors above")
        
    return 0 if all_ok else 1

if __name__ == "__main__":
    sys.exit(main())
