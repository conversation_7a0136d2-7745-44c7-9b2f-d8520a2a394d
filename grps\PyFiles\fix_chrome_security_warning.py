#!/usr/bin/env python3
"""
Chrome Security Warning Fix Script

This script fixes the "You are using an unsupported command-line flag: --disable-web-security" warning
by cleaning up Chrome processes and cached profile data.

Usage:
    python fix_chrome_security_warning.py
"""

import os
import sys
import json
import time
import psutil
import shutil
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def kill_chrome_processes():
    """Kill all Chrome and ChromeDriver processes"""
    logger.info("Killing all Chrome and ChromeDriver processes...")
    
    killed_processes = []
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                proc_name = proc.info['name']
                if proc_name and ('chrome' in proc_name.lower() or 'chromedriver' in proc_name.lower()):
                    logger.info(f"Terminating process: {proc_name} (PID: {proc.info['pid']})")
                    proc.terminate()
                    killed_processes.append(proc_name)
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        # Wait for processes to terminate
        time.sleep(2)
        
        # Force kill any remaining processes
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name']
                if proc_name and ('chrome' in proc_name.lower() or 'chromedriver' in proc_name.lower()):
                    logger.warning(f"Force killing stubborn process: {proc_name} (PID: {proc.info['pid']})")
                    proc.kill()
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
    except Exception as e:
        logger.error(f"Error killing Chrome processes: {e}")
        
    if killed_processes:
        logger.info(f"Successfully terminated {len(killed_processes)} Chrome processes")
    else:
        logger.info("No Chrome processes found to terminate")

def clear_chrome_profile_caches():
    """Clear Chrome profile caches that might contain old flags"""
    logger.info("Clearing Chrome profile caches...")
    
    # Get the profiles directory
    current_dir = Path(__file__).parent
    profiles_dir = current_dir.parent / "Profiles"
    
    if not profiles_dir.exists():
        logger.warning(f"Profiles directory not found: {profiles_dir}")
        return
        
    cleared_profiles = 0
    
    for profile_dir in profiles_dir.iterdir():
        if profile_dir.is_dir():
            try:
                # Clear Chrome preferences that might contain cached flags
                prefs_file = profile_dir / "Default" / "Preferences"
                if prefs_file.exists():
                    logger.info(f"Cleaning preferences for profile: {profile_dir.name}")
                    
                    with open(prefs_file, 'r', encoding='utf-8') as f:
                        prefs = json.load(f)
                    
                    # Remove problematic cached settings
                    changes_made = False
                    
                    if 'browser' in prefs:
                        if 'enabled_labs_experiments' in prefs['browser']:
                            del prefs['browser']['enabled_labs_experiments']
                            changes_made = True
                            
                        if 'command_line_flags' in prefs['browser']:
                            del prefs['browser']['command_line_flags']
                            changes_made = True
                    
                    # Clear any cached startup URLs that might trigger warnings
                    if 'session' in prefs:
                        if 'startup_urls' in prefs['session']:
                            prefs['session']['startup_urls'] = ["about:blank"]
                            changes_made = True
                    
                    if changes_made:
                        with open(prefs_file, 'w', encoding='utf-8') as f:
                            json.dump(prefs, f, indent=2)
                        logger.info(f"Updated preferences for profile: {profile_dir.name}")
                        cleared_profiles += 1
                
                # Clear other cache files that might contain flags
                cache_files = [
                    "Default/Local State",
                    "Default/Secure Preferences",
                    "Local State"
                ]
                
                for cache_file in cache_files:
                    cache_path = profile_dir / cache_file
                    if cache_path.exists():
                        try:
                            # Backup and clear problematic entries
                            if cache_file.endswith("Preferences") or cache_file.endswith("Local State"):
                                with open(cache_path, 'r', encoding='utf-8') as f:
                                    data = json.load(f)
                                
                                # Remove any command line related entries
                                if 'browser' in data and 'command_line' in data['browser']:
                                    del data['browser']['command_line']
                                    
                                    with open(cache_path, 'w', encoding='utf-8') as f:
                                        json.dump(data, f, indent=2)
                                    logger.info(f"Cleaned cache file: {cache_file}")
                        except:
                            # If we can't parse as JSON, skip it
                            pass
                            
            except Exception as e:
                logger.warning(f"Error cleaning profile {profile_dir.name}: {e}")
                
    logger.info(f"Cleaned {cleared_profiles} Chrome profiles")

def verify_code_fix():
    """Verify that the --disable-web-security flag has been removed from code"""
    logger.info("Verifying code fix...")
    
    current_dir = Path(__file__).parent
    updated_groups_file = current_dir / "updated_groups.py"
    
    if not updated_groups_file.exists():
        logger.error("updated_groups.py not found!")
        return False
        
    try:
        with open(updated_groups_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for the problematic flag
        if '--disable-web-security' in content:
            # Check if it's only in comments
            lines_with_flag = []
            for i, line in enumerate(content.split('\n'), 1):
                if '--disable-web-security' in line:
                    lines_with_flag.append((i, line.strip()))
            
            # Check if all occurrences are commented out or in verification code
            all_commented = True
            for line_num, line in lines_with_flag:
                # Get the full context around this line to better understand it
                lines = content.split('\n')
                context_start = max(0, line_num - 3)
                context_end = min(len(lines), line_num + 2)
                context = '\n'.join(lines[context_start:context_end])

                # Skip if it's a comment, removed flag, or part of verification/detection code
                is_safe = (line.strip().startswith('#') or
                          '# REMOVED:' in line or
                          'problematic_flags' in context or
                          'Check for problematic flags' in context or
                          '_verify_chrome_flags' in context or
                          'def _verify_chrome_flags' in context)

                if not is_safe:
                    all_commented = False
                    logger.error(f"Line {line_num}: Active --disable-web-security flag found: {line}")
                else:
                    logger.debug(f"Line {line_num}: Safe occurrence (verification code): {line.strip()}")
                    
            if all_commented:
                logger.info("✅ All --disable-web-security flags are properly commented out")
                return True
            else:
                logger.error("❌ Active --disable-web-security flags found in code!")
                return False
        else:
            logger.info("✅ No --disable-web-security flags found in code")
            return True
            
    except Exception as e:
        logger.error(f"Error verifying code: {e}")
        return False

def main():
    """Main function to fix Chrome security warning"""
    logger.info("🚀 Starting Chrome Security Warning Fix...")
    logger.info("=" * 50)
    
    try:
        # Step 1: Kill Chrome processes
        kill_chrome_processes()
        
        # Step 2: Clear profile caches
        clear_chrome_profile_caches()
        
        # Step 3: Verify code fix
        code_ok = verify_code_fix()
        
        logger.info("=" * 50)
        
        if code_ok:
            logger.info("✅ Chrome security warning fix completed successfully!")
            logger.info("The --disable-web-security warning should no longer appear.")
            logger.info("\nNext steps:")
            logger.info("1. Restart your application")
            logger.info("2. The warning should be gone")
            logger.info("3. If you still see the warning, it might be from a different source")
        else:
            logger.error("❌ Fix incomplete - please check the code issues above")
            
    except Exception as e:
        logger.error(f"Error during fix process: {e}")
        return 1
        
    return 0

if __name__ == "__main__":
    sys.exit(main())
